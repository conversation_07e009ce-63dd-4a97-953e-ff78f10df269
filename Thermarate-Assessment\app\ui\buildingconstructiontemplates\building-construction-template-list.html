<section id="buildingconstructiontemplate-list-view" class="main-content-wrapper" data-ng-controller="BuildingConstructionTemplateListCtrl as vm">

    <div class="widget">
        <div data-cc-widget-header title="{{vm.title}}"></div>
        <div data-cc-widget-action-bar
                data-quick-find-model='vm.listFilter'
                data-quick-find-holder="Search"
                data-action-buttons='vm.actionButtons'
                data-refresh-list='vm.refreshList()'
                data-spinner-busy='vm.isBusy'
                data-filter-options="vm.filterOptions"
                data-filter-changed="vm.refreshList(value)"
                data-current-filter="vm.currentFilter"
                data-query-builder-name="ConstructionTemplate">
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.constructionTemplateList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="ConstructionTemplate"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th st-sort="templateName" class="can-sort text-left">Template Name</th>
                        <th st-sort="notes" class="can-sort text-left">Notes</th>
                        <th style="width: 10%;"></th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.constructionTemplateList" class="list-row clickable">
                        <td data-title="Name" ng-click="vm.goToItem(row)">
                            <div style="width: 100%; padding-left: 10px; padding-right: 40px; box-sizing: border-box; text-align: left;">
                                {{row.templateName}}
                                <div class="go-to-variation-button" style="order:3;"> <img src="/content/images/arrow-right.png" /> </div>
                            </div>
                        </td>
                        <td data-title="Notes" class="text-left">{{::row.notes}}</td>
                        <td data-title="Notes" class="text-center" >
                            <div layout="row"
                                 layout-align="center center">
                                <md-button class="md-raised md-icon-button"
                                           redi-enable-roles="admin__template__delete"
                                           title="Delete Template"
                                           ng-click="vm.delete(row)">
                                    <i class="fa fa-eraser fa-lg"></i>
                                </md-button>

                                <md-button ng-if="item.isLocked != true"
                                           redi-enable-roles="admin__template__create"
                                           style="margin-right: 5px;"
                                           class="md-raised md-icon-button"
                                           title="Copy Template"
                                           ng-click="vm.clone(row)">
                                        <i class="fa fa-clone fa-lg"></i>
                                </md-button>
                            </div>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="5" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>

<style>

    .list-row {
        height: 52px;
    }

    .list-row:hover .go-to-variation-button {
        visibility: visible;
    }

    .go-to-variation-button {
        visibility: hidden;
        position: absolute;
        top: 50%; transform: translateY(-50%);
        right: 7%;
        width: 25px;
        height: 25px;
        min-width: 25px;
        min-height: 25px;
        border-radius: 4px;
        cursor: pointer;
    }

        .go-to-variation-button:hover {
            background-color: #d1d1d1;
        }

        .go-to-variation-button > img {
            position: absolute;
            top: 50%;
            left: 54%;
            transform: translate(-50%, -50%);
            width: 60%;
            height: auto;
        }

</style>