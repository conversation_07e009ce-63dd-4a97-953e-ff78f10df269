(function () {
    // The FrequencyUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'FrequencyUpdateCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state',  'frequencyservice', frequencyUpdateController]);
function frequencyUpdateController($rootScope, $scope, $mdDialog, $stateParams, $state,  frequencyservice) {
        // The model for this form 
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        var eventListenerList = [];
        vm.title = 'Edit Frequency';
        vm.previousRoute = $rootScope.previousState;
        vm.viewMode = $scope.viewMode;
        vm.isModal = $scope.modalInstance != null;
        vm.hideActionBar = false;
        vm.frequencyCode = null;
        vm.frequency = {};
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        if (vm.newRecord) {
            vm.title = "New Frequency";
            // Set any default values required for a new record.
        }

        if (vm.isModal) {
            if(vm.newRecord == false){
                vm.frequencyCode = $scope.frequencyCode;
            }
            vm.hideActionBar = true;
        } else {
            vm.frequencyCode = $stateParams.frequencyCode;
        }

        // Get data for object to display on page
        var frequencyCodePromise = null;
        if (vm.frequencyCode != null) {
            frequencyCodePromise = frequencyservice.getFrequency(vm.frequencyCode)
            .then(function (data) {
                if (data != null) {
                    vm.frequency = data;
                }
                vm.isBusy = false;
            });
        }
        else {
            vm.isBusy = false;
        }

        // Get data for any dropdown lists

        // Functions to get data for Typeahead

        $scope.$on('$destroy', function () {
            for(var i = 0, len = eventListenerList; i < len; i++){
                // Destroy each registered listener
                eventListenerList[i]();
            }
            eventListenerList = [];
        });

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            }
            else {
                if (vm.previousRoute != undefined && vm.previousRoute != null) {
                    $state.go(vm.previousRoute);
                }
                else {
                    $state.go("frequency-list");
                }
            }
        }

        vm.save = function () {
            if(vm.newRecord == true){
                frequencyservice.createFrequency(vm.frequency).then(function(data){
                    vm.frequency = data;
                    vm.frequencyCode = vm.frequency.frequencyCode;
                    vm.cancel();
                });
            }else{
                frequencyservice.updateFrequency(vm.frequency).then(function(data){
                    if (data != null) {
                        vm.frequency = data;
                        vm.frequencyCode = vm.frequency.frequencyCode;
                    }
                });
            }
        }

        vm.delete = function () {
            frequencyservice.deleteFrequency(vm.frequencyCode).then(function () {
                vm.cancel();
            });
        }

        vm.undoDelete = function () {
            frequencyservice.undoDeleteFrequency(vm.frequencyCode).then(function () {
                vm.cancel();
            });
        }

    }
})();