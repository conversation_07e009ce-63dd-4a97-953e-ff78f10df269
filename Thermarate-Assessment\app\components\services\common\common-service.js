(function () {
    'use strict';

    // Define the common module 
    // Contains services:
    //  - common
    //  - logger
    //  - spinner
    var commonModule = angular.module('common', ['ngMaterial']);

    // Must configure the common service and set its 
    // events via the commonConfigProvider
    commonModule.provider('commonConfig', function () {
        this.config = {
            // These are the properties we need to set
            //controllerActivateSuccessEvent: '',
            //spinnerToggleEvent: ''
        };

        this.$get = function () {
            return {
                config: this.config
            };
        };
    });

    commonModule.factory('common', ['$q', '$rootScope', '$timeout', 'commonConfig', 'logger', 'buildqueryparameters', 'Upload', common]);

    function common($q, $rootScope, $timeout, commonConfig, logger, buildqueryparameters, Upload) {
        var throttles = {};

        extendString();
        extendQ();

        var service = {
            // common angular dependencies
            $broadcast: $broadcast,
            $q: $q,
            $timeout: $timeout,
            // generic
            activateController: activateController,
            createSearchThrottle: createSearchThrottle,
            debouncedThrottle: debouncedThrottle,
            logger: logger, // for accessibility
            buildqueryparameters: buildqueryparameters, // for accessibility
            isNumber: isNumber,
            textContains: textContains,
            textHasNumbers: textHasNumbers,
            emptyGuid: '00000000-0000-0000-0000-000000000000',
            newGuidComb: newGuidComb,
            filterById: filterById,
            filterByName: filterByName,
            filterByType: filterByType,
            getEntityByIdFromObj: getEntityByIdFromObj,
            getEntityManager: getEntityManager,
            getSaveErrorMessages: getSaveErrorMessages,
            getEntityValidationErrMsgs: getEntityValidationErrMsgs,
            deal: deal,
            groupArray: groupArray,
            keyArray: keyArray,
            toTitleCase: toTitleCase,
            firstCharUpperCase: firstCharUpperCase,
            firstCharLowerCase: firstCharLowerCase,
            toSplitTitleCase: toSplitTitleCase,
            setItemReferences: setItemReferences,
            addLeadingZero: addLeadingZero,
            setDrawingReferences: setDrawingReferences,
            nullAwareMerge,
            nullAwareMergeInPlace,
            resolve,
            delay,
            setSort,
            applySort,
            sortList,
            equalish,
            lessThanOrEqualish,
            forceBlurInputWithId,
            forceBlurInputsWithClass,
            passesSectorCondition,
            determineSectorType,
            roundUp,
            roundToStep,
            roundUpInt,
            getDisplayNumber,
            determineELHeatCoolResultColour,
            initialiseMultiFilters,
            applyMultiSelectChangedLogic,
            getMultiSelectDisplayText,
            getOptionsForAnySelected,
            targetEnergyRatingOptions,
            getMultiFilterSelectedText,
            getMultiSelectDropdownText,
            getOptionName,
            adjustFiltersForSelection,
            orderFilterOptions,
            anyOptionsSelectedOnField,
            anyFiltersApplied,
            getNewFilteredList,
            updateFilterCountData,
            uploadSoftwareFile,
            availableOptionsForItem,
            uniqueValues,
            initialiseSelectAll,
            selectAllLogic,
            symbol,
            getFileLines,
            stringNullOrEmpty,
            encodeForUrl,
            linkVariablesBetweenArrays
        };

        return service;

        function activateController(promises, controllerId) {
            return $q.all(promises).then(function (eventArgs) {
                var data = { controllerId: controllerId };
                $broadcast(commonConfig.config.controllerActivateSuccessEvent, data);
            });
        }

        function $broadcast() {
            return $rootScope.$broadcast.apply($rootScope, arguments);
        }

        function createSearchThrottle(viewmodel, list, filteredList, filter, delay) {
            // custom delay or use default
            delay = +delay || 300;
            // if only vm and list parameters were passed, set others by naming convention 
            if (!filteredList) {
                // assuming list is named sessions,
                // filteredList is filteredSessions
                filteredList = 'filtered' + list[0].toUpperCase() + list.substr(1).toLowerCase(); // string
                // filter function is named sessionFilter
                filter = list + 'Filter'; // function in string form
            }

            // create the filtering function we will call from here
            var filterFn = function () {
                // translates to ...
                // vm.filteredSessions 
                //      = vm.sessions.filter(function(item( { returns vm.sessionFilter (item) } );
                viewmodel[filteredList] = viewmodel[list].filter(function(item) {
                    return viewmodel[filter](item);
                });
            };

            return (function () {
                // Wrapped in outer IFFE so we can use closure 
                // over filterInputTimeout which references the timeout
                var filterInputTimeout;

                // return what becomes the 'applyFilter' function in the controller
                return function(searchNow) {
                    if (filterInputTimeout) {
                        $timeout.cancel(filterInputTimeout);
                        filterInputTimeout = null;
                    }
                    if (searchNow || !delay) {
                        filterFn();
                    } else {
                        filterInputTimeout = $timeout(filterFn, delay);
                    }
                };
            })();
        }

        function debouncedThrottle(key, callback, delay, immediate) {
            var defaultDelay = 1000;
            delay = delay || defaultDelay;
            if (throttles[key]) {
                $timeout.cancel(throttles[key]);
                throttles[key] = undefined;
            }
            if (immediate) {
                callback();
            } else {
                throttles[key] = $timeout(callback, delay);
            }
        }

        function isNumber(val) {
            // negative or positive
            return /^[-]?\d+$/.test(val);
        }

        function textContains(text, searchText) {
            return text && -1 !== text.toLowerCase().indexOf(searchText.toLowerCase());
        }

        function textHasNumbers(text) {
            return /\d/.test(text);
        }

        /*********************************************************
        * @method to$q {Promise} Convert a Q.js promise into an angular $q
        * and optionally add a $q.then(sucess, fail) to the returned $q promise.
        * @param promiseQ {Promise} the Q.js promise to convert
        * The Q promise must return some value when they succeed or
        * rethrow the error if they fail. Else this method logs an error.
        * @param [success] {function} optional success callback for the $q.then()
        * @param [fail] {function} optional fail callback for the $q.then()
        *********************************************************/
        function to$q(qPromise, success, fail) {
            var d = $q.defer();
            qPromise
                .then(function (data) {
                    if (data === undefined) {
                        logger.logError("Programming error: no data. " +
                        "Perhaps success callback didn't return a value or " +
                            "fail callback didn't re-throw error");
                        // If an error is caught and not rethrown in an earlier promise chain
                        // will arrive here with data === undefined. 
                        // Neglecting to re-throw is a common, accidental omission.
                        // To be safe, have every success callback return something
                        // and trap here if data is ever undefined
                    }
                    d.resolve(data);
                    $rootScope.$apply();// see https://groups.google.com/forum/#!topic/angular/LQoBCQ-V_tM
                })
                .fail(function (error) {
                    d.reject(error);
                    $rootScope.$apply();// see https://groups.google.com/forum/#!topic/angular/LQoBCQ-V_tM
                });
            if (success || fail) {
                d.promise = d.promise.then(success, fail);
            }
            return d.promise;
        }

        // monkey patch this method into Q.js' promise prototype
        function extendQ() {
            var promise = Q.defer().promise;
            var fn = Object.getPrototypeOf(promise);
            if (fn.to$q) return; // already extended
            fn.to$q = function (success, fail) { return to$q(this, success, fail); };
        }

        /*********************************************************
        * Generate a new GuidCOMB Id (sequential for MS SQL Server)
        * @method newGuidComb {String}
        * @param [n] {Number} Optional integer value for a particular time value
        * if not supplied (and usually isn't), n = new Date.getTime()
        *********************************************************/
        function newGuidComb(n) {
            // Create a pseudo-Guid whose trailing 6 bytes (12 hex digits) are timebased
            // Start either with the given getTime() value, n, or get the current time in ms.
            // Each new Guid is greater than next if more than 1ms passes
            // See http://thatextramile.be/blog/2009/05/using-the-guidcomb-identifier-strategy
            // Based on breeze.core.getUuid which is based on this StackOverflow answer
            // http://stackoverflow.com/a/2117523/200253     
            // Convert time value to hex: n.toString(16)
            // Make sure it is 6 bytes long: ('00'+ ...).slice(-12) ... from the rear
            // Replace LAST 6 bytes (12 hex digits) of regular Guid (that's where they sort in a Db)
            // Play with this in jsFiddle: http://jsfiddle.net/wardbell/qS8aN/
            var timePart = ('00' + (n || (new Date().getTime())).toString(16)).slice(-12);
            return 'xxxxxxxx-xxxx-4xxx-yxxx-'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0,
                    v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            }) + timePart;
        }
        /*********************************************************
        * Array filter factories
        *********************************************************/
        function filterById(array) {
            return function (id) {
                var item = array.filter(function (x) { return x.id == id; });//"==" ok; want coercion
                return item[0] || null;
            };
        }
        function filterByName(array) {
            // name is either a regExp or a string which is converted to a regex ignore case
            return function (name) {
                var re = (typeof name === 'string') ? new RegExp(name, 'i') : name;
                return array.filter(function (x) { return re.test(x.name); });
            };
        }
        function filterByType(array) {
            return function (type) {
                // type is either a regExp or a string which is converted to a regex ignore case
                var re = (typeof type === 'string') ? new RegExp(type, 'i') : type;
                return array.filter(function (x) { return re.test(x.type); });
            };
        }

        /** Complex type helpers **/
        function getEntityByIdFromObj(obj, typeName, id) {
            var em = getEntityManager(obj);
            return (em) ? em.getEntityByKey(typeName, id) : null;
        }

        function getEntityManager(obj) {
            if (obj.complexAspect) {
                return obj.complexAspect.getEntityAspect().entityManager;
            } else if (obj.entityAspect) {
                return obj.entityAspect.entityManager;
            } else {
                return null;
            }
        }

        /*********************************************************
        * Handle save error messages
        *********************************************************/
        function getSaveErrorMessages(error) {
            var msg = error.message;
            var detail = error.detail;
            if (msg.match(/validation error/i)) {
                return getValidationMessages(error);
            } else if (detail && detail.ExceptionType &&
                detail.ExceptionType.indexOf('OptimisticConcurrencyException') !== -1) {
                // Concurrency error 
                return "Another user, perhaps the server, " +
                    "may have changed or deleted an entity in the change-set.";
            }
            return msg;
        }

        function getValidationMessages(error) {

            var detail = error.detail;

            if (detail) { // Failed validation on the server
                try {
                    return 'Server ' + detail.ExceptionMessage + '\nStackTrace: ' + detail.StackTrace;
                } catch (e) {
                    return 'Server ' + error.message;
                }
            }

            // Failed on client during pre-Save validation
            try {
                return error.entitiesWithErrors.map(function (entity) {
                    return entity.entityAspect.getValidationErrors().map(function (valError) {
                        return valError.errorMessage;
                    }).join(', \n');
                }).join('; \n');
            }
            catch (e) {
                return "validation error (error parsing exception :'" + e.message + "')";
            }
        }

        /*********************************************************
        * Return an entity's validation error messages as a string
        *********************************************************/
        function getEntityValidationErrMsgs(entity) {
            var errs = entity.entityAspect.getValidationErrors();
            return errs.length ?
                errs.map(function (err) { return err.errorMessage; }).join(", ") :
                "no errors";
        }

        /*******************************************************
        * String extensions
        * Monkey punching JavaScript native String class
        * w/ format, startsWith, endsWith
        * go ahead and shoot me but it's convenient 
        ********************************************************/
        function extendString() {
            var stringFn = String.prototype;
            if (stringFn.format) { return; } // already extended

            // Ex: "{0} returned {1} item(s)".format(queryName, count));
            stringFn.format = stringFn.format || function () {
                var s = this;
                for (var i = 0, len = arguments.length; i < len; i++) {
                    var reg = new RegExp("\\{" + i + "\\}", "gm");
                    s = s.replace(reg, arguments[i]);
                }

                return s;
            };

            stringFn.endsWith = stringFn.endsWith || function (suffix) {
                return (this.substr(this.length - suffix.length) === suffix);
            };

            stringFn.startsWith = stringFn.startsWith || function (prefix) {
                return (this.substr(0, prefix.length) === prefix);
            };

            stringFn.contains = stringFn.contains || function (value) {
                return (this.indexOf(value) !== -1);
            };
        }

        /*********************************************************
        * Deal an array of things into "hands" as if dealing cards. 
        * e.g. deal([1,2,3,4,5,6,7], 3) -> [[1,4,7],[2,5],[3,6]]
        *********************************************************/
        function deal(arr, numHands) {
            var hands = new Array(numHands);
            var i, len = arr.length, hand;
            for (i = 0; i < numHands; i++) {
                hands[i] = [];
            }
            for (i = 0; i < len; i++) {
                hand = Math.ceil(i % numHands);
                hands[hand].push(arr[i]);
            }
            return hands;
        }

        /*********************************************************
        // Group an array of objects by an object property. Each element of the returned array
        // is a object { keyName: key, valueName: [{...},...] }
        // arr: array of objects
        // keyfn: function to get the desired group key from each object
        // keyName: name of key property in resulting objects (defaults to 'key')
        // valueName: name of values property in resulting objects (defaults to 'values')
        // returns: array of key,values objects, where the values are objects from the original array.
        // See utilSpec.js for an example.
        *********************************************************/
        function groupArray(arr, keyfn, keyName, valueName) {
            keyName = keyName || 'key';
            valueName = valueName || 'values';
            var groupMap = {};
            var groupList = [];
            arr.forEach(function (o) {
                var key = keyfn(o);
                var group = groupMap[key];
                if (!group) {
                    group = {};
                    group[keyName] = key;
                    group[valueName] = [];
                    groupMap[key] = group;
                    groupList.push(group);
                }
                group[valueName].push(o);
            });
            return groupList;
        }

        /*********************************************************
        // Convert an array into an object.  The returned object has keys defined by the keyfn,
        // and values from the original array.  If there are duplicate keys, the resulting object
        // has the value of the last key.
        // arr: array of objects
        // keyfn: function to get the desired group key from each object
        // See utilSpec.js for an example.
        *********************************************************/
        function keyArray(arr, keyfn) {
            var map = {};
            arr.forEach(function (o) {
                var key = keyfn(o);
                map[key] = o;
            });
            return map;
        }

        function firstCharUpperCase(inString) {
            return inString.charAt(0).toUpperCase() + inString.substr(1);
        }

        function firstCharLowerCase(inString) {
            return inString.charAt(0).toLowerCase() + inString.substr(1);
        }

        function toTitleCase(data) {
            var smallWords = /^(a|an|and|as|at|but|by|en|for|if|in|nor|of|on|or|per|the|to|vs?\.?|via)$/i;
            if (data == null) {
                return data;
            }
            data = data.toLowerCase();
            return data.replace(/[A-Za-z0-9\u00C0-\u00FF]+[^\s-]*/g, function (match, index, title) {
                if (index > 0 && index + match.length !== title.length &&
                  match.search(smallWords) > -1 && title.charAt(index - 2) !== ":" &&
                  (title.charAt(index + match.length) !== '-' || title.charAt(index - 1) === '-') &&
                  title.charAt(index - 1).search(/[^\s-]/) < 0) {
                    return match.toLowerCase();
                }

                if (match.substr(1).search(/[A-Z]|\../) > -1) {
                    return match;
                }

                return match.charAt(0).toUpperCase() + match.substr(1);
            });
        }

        /**
         * Converts camelCaseConstructs to Title Case Constructs.
         * Stolen from https://stackoverflow.com/questions/7225407/convert-camelcasetext-to-sentence-case-text
         */
        function toSplitTitleCase(inputString) {
            
            if(inputString == null)
                return ""; 
            
            var result = inputString                                // "__ToGetYourGEDInTimeASongAboutThe26ABCsIsOfTheEssenceButAPersonalIDCardForUser_456InRoom26AContainingABC26TimesIsNotAsEasyAs123ForC3POOrR2D2Or2R2D"
                .replace(/(_)+/g, ' ')                              // " ToGetYourGEDInTimeASongAboutThe26ABCsIsOfTheEssenceButAPersonalIDCardForUser 456InRoom26AContainingABC26TimesIsNotAsEasyAs123ForC3POOrR2D2Or2R2D"
                .replace(/([a-z])([A-Z][a-z])/g, "$1 $2")           // " To Get YourGEDIn TimeASong About The26ABCs IsOf The Essence ButAPersonalIDCard For User456In Room26AContainingABC26Times IsNot AsEasy As123ForC3POOrR2D2Or2R2D"
                .replace(/([A-Z][a-z])([A-Z])/g, "$1 $2")           // " To Get YourGEDIn TimeASong About The26ABCs Is Of The Essence ButAPersonalIDCard For User456In Room26AContainingABC26Times Is Not As Easy As123ForC3POOr R2D2Or2R2D"
                .replace(/([a-z])([A-Z]+[a-z])/g, "$1 $2")          // " To Get Your GEDIn Time ASong About The26ABCs Is Of The Essence But APersonal IDCard For User456In Room26AContainingABC26Times Is Not As Easy As123ForC3POOr R2D2Or2R2D"
                .replace(/([A-Z]+)([A-Z][a-z][a-z])/g, "$1 $2")     // " To Get Your GEDIn Time A Song About The26ABCs Is Of The Essence But A Personal ID Card For User456In Room26A ContainingABC26Times Is Not As Easy As123ForC3POOr R2D2Or2R2D"
                .replace(/([a-z]+)([A-Z0-9]+)/g, "$1 $2")           // " To Get Your GEDIn Time A Song About The 26ABCs Is Of The Essence But A Personal ID Card For User 456In Room 26A Containing ABC26Times Is Not As Easy As 123For C3POOr R2D2Or 2R2D"

            var result = result.charAt(0).toUpperCase() + result.slice(1);

            return result;
        }

        /**
         * Sets the item references (Essentially the user-friendly ID) for all items in the given array,
         * where field name is the variable to set, mostly 'item reference' but somtimes 'reason' or 'item'
         * prefix goes before the index eg the fourth item in the external floor array will have an item ref = 'EF4';
         * 
         * @param {any} array The array of items to work on (e.g. the ArtificialLighting array)
         * @param {any} fieldName The property of the object to modify using Javascripts property-indexing notation.
         * @param {any} prefix String to prefix the item reference by, eg "AL" for "Artificial Lighting"
         * @param {any} pad ???
         * @param {any} customArchive ???
         */
        function setItemReferences(array, fieldName, prefix, pad, customArchive) {
            var count = 0;
            for (var ii = 0; ii < array.length; ii++) {
                if (pad == true) {
                    if (customArchive != undefined) {
                        if (array[ii].archived == customArchive) {
                            array[ii][fieldName] = prefix + addLeadingZero((count + 1));
                            count++
                        } else {
                            array[ii][fieldName] = 0;
                        }
                    } else {
                        array[ii][fieldName] = prefix + addLeadingZero((count + 1));
                        count++;
                    }
                } else {
                    if (customArchive != undefined) {
                        if (array[ii].archived == customArchive) {
                            array[ii][fieldName] = prefix + (count + 1);
                            count++;
                        } else {
                            array[ii][fieldName] = 0;
                        }
                    } else {
                        array[ii][fieldName] = prefix + (count + 1);
                        count++;
                    }
                }
            }
        }

        /**
         * Sets the item references (Essentially the user-friendly ID) for all drawings in the given array, IGNORING
         * any that have deleted = true set.
         */
        function setDrawingReferences(drawings) {

            if (drawings == null)
                return;

            const fieldName = 'drawingNumber';

            var count = 0;

            for (var ii = 0; ii < drawings.length; ii++) {

                if (drawings[ii].deleted != true) {
                    drawings[ii][fieldName] = (count + 1);
                    count++;
                }
            }
        }

        /** 
         * Add leading zero to numbers between 1 and 9. NOTE: Won't work for negative numbers! 
         *  
         * @param {number} number The number to pad (if required).
         * @param {number} maxLength The maximum number of total characters in the string.
         * @returns {string} A string with the padding applied!
         */
        function addLeadingZero(number, maxLength = 2) {
            return number.toString().padStart(maxLength, "0");
        }
        

        /**
         * Returns the result of mergeing 2 objects together without modifying the objects themselves. If the same key 
         * exists across base and updated objects, but the updated objects key is NULL, the base key value is retained.
         */
        function nullAwareMerge(base, updates) {
            let res = {};
            Object.keys({ ...base, ...updates }).map(key => {
                res[key] = updates[key] != null ? updates[key] : base[key];
            });
            return res;
        }

        /**
         * Merges values from the 'updates' object into the base object
         */
        function nullAwareMergeInPlace(base, updates) {
            Object.keys({ ...base, ...updates }).map(key => {
                base[key] = updates[key] != null ? updates[key] : base[key];
            });
        }

        /**
         * Allows you to use a string to get deeply-nested properties from an object.
         * Taken from: https://stackoverflow.com/questions/6491463/accessing-nested-javascript-objects-and-arrays-by-string-path
         * 
         * @example let x = common.resolve("someProperty.deeplyNested", someObject);
         * 
         * @param {any} path Path to the property you want access to.
         * @param {any} obj Object you wish to search.
         * @param {any} separator
         */
        function resolve(path, obj = self, separator = '.') {
            var properties = Array.isArray(path) ? path : path.split(separator)
            return properties.reduce((prev, curr) => prev && prev[curr], obj)
        }

        /*
         */
        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }


        /**
         * Determines the sort order for a column depending on its current
         * state. The based in SortInfo object is used to track sorts across
         * a table (though at the moment we only support sorting on one column).
         *
         * Does NOT sort any data. For that, see applySort
         *
         * @param {string} column The new column (object property) to sort on.
         * Supports nested columns. I.e. you can sort of "property.nestedProp".
         *
         * @param { SortInfo } sortInfo The object which keeps track of which
         * column and direction to sort on.
         * 
         * @param transform Optional callback which can be used to apply 
         * transformations to raw column data.
         */
        function setSort(column, sortInfo, transform = null) {
            
            // Always nullify this unless the caller explicitly sets it.
            sortInfo.transform = transform;
            
            // Switch between ASC/DESC/Natural
            if (sortInfo.direction === null || sortInfo.column !== column)
                sortInfo.direction = "ASC";
            else if (sortInfo.direction === "ASC")
                sortInfo.direction = "DESC";
            else if(sortInfo.direction === "DESC")
                sortInfo.direction = null;

            // Nullify column if we're back to our natural sort state.
            if (sortInfo.direction === null)
                sortInfo.column = null;
            else
                sortInfo.column = column;
            
        }

        /**
         * Returns a NEW array of the given data sorted by property/direction
         * as defined in the sortInfo parameter.
         *
         * Be sure to call 'setSort' on a SortInfo object before calling this.
         *
         * @param { T[] } data Generic array to filter.
         *
         * @param { SortInfo } sortInfo The object which keeps track of which
         * column and direction to sort on.
         *
         * @param { function } transformCallback Optional callback which can 
         * transform the given data value (for instance, if you wish to sort
         * on something that is a GUID that relates to a textual string).
         * 
         * @returns { T[] | null } A new array with all data sorted OR null (if
         * we are back to the 'natural' order). Although the array is new,
         * the objects within it will point to the originals.
         */
        function applySort(data, sortInfo) {
            
            if(sortInfo == null || data == null)
                return null;
            
            // Make copy of data since we want to retain original.
            let sortedData = [...data];
            
            if(sortInfo.column === null)
                return null; // Don't sort anything and return a null array.

            if (sortInfo.column != null && sortInfo.direction != null) {

                if (sortInfo.direction === "ASC") {

                    sortedData.sort((a, b) => {

                        let ac = resolve(sortInfo.column, a);
                        let bc = resolve(sortInfo.column, b);
                        
                        // Perform further custom transforms if required.
                        if(sortInfo.transform != null) {
                            ac = sortInfo.transform(ac);
                            bc = sortInfo.transform(bc);
                        }

                        if (typeof ac == 'string' && bc != null) {
                            return ac.localeCompare(bc);
                        } else {
                            return (ac || 0) - (bc || 0);
                        }
                    });

                } else if (sortInfo.direction === "DESC") {

                    sortedData.sort((a, b) => {

                        let ac = resolve(sortInfo.column, a);
                        let bc = resolve(sortInfo.column, b);

                        // Perform further custom transforms if required.
                        if(sortInfo.transform != null) {
                            ac = sortInfo.transform(ac) || null;
                            bc = sortInfo.transform(bc) || null;
                        }

                        if (typeof ac == 'string' && bc != null) {
                            return bc.localeCompare(ac);
                        } else {
                            return (bc || 0) - (ac || 0);
                        }
                    });
                }
            } else {

                // Sort by nothing then - null indicates caller should use 'natural' order.
                return null;
                
            }
            
            return sortedData;
        }

        /**
         * Returns true if the 2 values passed in are "close enough" to equal.
         * Useful when you want to compare a user input value like 1.08 against a
         * calculated value like 1.08111111
         *
         * @param {any} decimimalPlaces Number of decimal places to check.
         */
        function equalish(x, y, decimalPlaces = 2) {
            
            if(x == null || y == null)
                return false;
            
            return parseFloat(x.toFixed(decimalPlaces)) == parseFloat(y.toFixed(decimalPlaces));
        }
        
        function lessThanOrEqualish(x, y, decimalPlaces = 2) {
            return x < y || equalish(x, y, decimalPlaces);
        }

        /** Purely used to force a redraw on inputs with the formatted-number directive */
        function forceBlurInputWithId(id) {
            setTimeout(() => {

                const input = document.getElementById(id);
                
                if(input == null)
                    return;

                // We need to enable the input first, and then set back to disabled if required.
                const  initialDisabledState = input.disabled;
                input.disabled = false;
                input.focus();
                input.blur();
                input.disabled = initialDisabledState;

            }, 50);
        }
        
        function forceBlurInputsWithClass(htmlClass) {

            // brrrrrrrr
            setTimeout(() => {

                const inputs = Array.from(document.getElementsByClassName(htmlClass));

                if(inputs == null)
                    return;
                
                inputs.forEach(input => {

                    // We need to enable the input first, and then set back to disabled if required.
                    const  initialDisabledState = input.disabled;
                    input.disabled = false;
                    input.focus();
                    input.blur();
                    input.disabled = initialDisabledState;
                    
                });
                
            }, 50);
        }

        
        function passesSectorCondition(value1, operator, value2) {

            let result = false;

            switch (operator) {
                case '<':
                    result = value1 < value2;
                    break;
                case '<=':
                    result = value1 <= value2;
                    break;
                case '>':
                    result = value1 > value2;
                    break;
                case '>=':
                    result = value1 >= value2;
                    break;
                default:
                    throw "Unhandled operator: " + operator;
                    break;
            }

            return result;
        }

        function determineSectorType(azimuth, sectors) {

            if (azimuth == null || !sectors) // Can be 0
                return;

            let sector = null;

            // Find the label of the sector that passes the condition with this azimuth.
            const found = sectors.find(sector =>
                passesSectorCondition(azimuth, sector.operator1, sector.min) &&
                passesSectorCondition(azimuth, sector.operator2, sector.max));

            if (found)
                sector = found.label;

            return sector;
        }


        function roundToStep(value, step) {
            step || (step = 1.0);
            var inv = 1.0 / step;
            return Math.round(value * inv) / inv;
        }

        /**
         * @param num The number to round
         * @param precision The number of decimal places to preserve
         * 
         * Attribution: https://stackoverflow.com/a/5191133
         */
        function roundUp(num, precision) {
            precision = Math.pow(10, precision)
            return Math.ceil(num * precision) / precision
        }
        
        function roundUpInt(number, rounding, decimalsWhenNull = 2) {
            
            if(rounding == null)
                return number.toFixed(decimalsWhenNull); // euugrhr
            
            return Math.ceil(number / rounding) * rounding;
        }
        
        /**
         * Formats a number
         */
        function getDisplayNumber(val, numDecimals, showZeros) {
                    
            // Split whole num and decimal (if more than one '.', just use first one)
            let elements = val.toString().split('.');

            // Remove symbols
            elements[0] = elements[0].replace(/[^0-9]/g, '');
            if (elements.length > 1) {
                elements[1] = elements[1].replace(/[^0-9]/g, '');
            }

            // IF has decimal and want to round decimals, round
            if (elements.length > 1 && numDecimals != null && elements[1].length > numDecimals) {
                elements[1] = Number(elements[0] + '.' + elements[1]).toFixed(numDecimals).split('.')[1];
            }

            // Add commas to whole number
            elements[0] = val.toString().length > 0 ? Number(elements[0]).toLocaleString() : '';

            // IF want to add zeros, add if needed
            if (showZeros) {
                if (elements.length == 1) {
                    elements.push("");
                }
                for (let i = elements[1].length; i < numDecimals; i++) {
                    elements[1] = elements[1] + "0";
                }
            }

            // Put back together
            let displayValue = elements.length > 1 && numDecimals != 0
                                ? elements[0] + '.' + elements[1]
                                : elements[0];

            return displayValue;
        }
        
        // Determine colour for energy labs heating/cooling results
        function determineELHeatCoolResultColour(heatingCoolingLoadLimits, option, optionVar, limitVariable) {
            if (option.assessmentMethod == "House Energy Rating (HER)" && heatingCoolingLoadLimits === false || option.energyLoadLimits[limitVariable] == null) {
                return 'black';
            } else if (option[optionVar] <= option.energyLoadLimits[limitVariable]) {
                return 'var(--thermarate-green)';
            } else {
                return 'var(--warning)';
            }
        }



        // ------------- //
        // - Filtering - //
        // ------------- //
        
        function initialiseMultiFilters(filters, list, filterOptions, appliedFilters) {
            // Set options for each filter
            for (let i = 0; i < filters.length; i++) {
                let field = filters[i].field;
                let isOptional = false;
                filterOptions[field] = [];
                for (let j = 0; j < list.length; j++) {
                    let job = list[j];
                    if (job[field] != null && (job[field] != '' || job[field] == 0) && !filterOptions[field].some(option => option.value?.toString().toLowerCase() == job[field]?.toString().toLowerCase())) {
                        filterOptions[field].push( { name: job[field], value: job[field] } );
                    }
                    // IF value is empty, add "Not Specified" option
                    else if (job[field] == null || job[field] == '') {
                        isOptional = true;
                    }
                }
                filterOptions[field] = filterOptions[field].sort( (a,b) => {
                    if (isNaN(a.name) && isNaN(b.name))
                        return a.name.toLowerCase() > b.name.toLowerCase() ? 1 : -1;
                    else
                        return a.name > b.name ? 1 : -1;
                });
                if (isOptional) {
                    filterOptions[field].unshift({ name: 'Not Specified', value: 'Not Specified' });
                }
                filterOptions[field].unshift({ name: 'Any', value: 'Any' });
                if (appliedFilters[field] == null || appliedFilters[field].length == 0) {
                    appliedFilters[field] = ['Any']
                }
            }
        }

        // Account for 'Any' option in a multi-select dropdown
        function applyMultiSelectChangedLogic(newUiSelections, oldSelections, ignoreKeys) {
            for (const key in newUiSelections) {
                if (ignoreKeys != null && ignoreKeys.includes(key))
                    continue;
                // IF other items selected then 'Any' selected, only select 'Any'
                if (oldSelections[key] != null && !oldSelections[key]?.some(x => x == 'Any') && newUiSelections[key]?.some(x => x == 'Any')) {
                    newUiSelections[key] = ['Any'];
                }
                // IF another item selected after 'Any' selected, remove 'Any' selection
                else if (oldSelections[key] == null && newUiSelections[key]?.length > 1 && newUiSelections[key]?.some(x => x == 'Any')) {
                    newUiSelections[key] = newUiSelections[key].filter(x => x != 'Any');
                }
                // IF empty, select 'Any'
                else if (newUiSelections[key] == null || newUiSelections[key]?.length == 0) {
                    newUiSelections[key] = ['Any'];
                }
            }
            // Copy UI selections to be formatted for service call by changing 'Any' to null
            let newServiceData = angular.copy(newUiSelections);
            for (const key in newServiceData) {
                if (ignoreKeys != null && ignoreKeys.includes(key))
                    continue;
                if (newServiceData[key][0] == 'Any') {
                    delete newServiceData[key];
                }
            }
            return newServiceData;
        }

        // Display text for multi select field that includes 'Any' option
        function getMultiSelectDisplayText(filterData, variable, customCheck, customFormat) {
            const selectedCount = filterData[variable]?.length;
            if (selectedCount === 1) {
                if (filterData[variable][0] == 'Any') {
                    return 'Any';
                }
                else if (customCheck) {
                    return customFormat(filterData[variable][0]);
                } else {
                    return filterData[variable][0];
                }
            }
            else {
                return `${selectedCount} Selected`;
            }
        }

        // For each input, if 'Any' selected, replace 'Any' with actual list of options
        function getOptionsForAnySelected(options, selections) {
            let newSelections = angular.copy(selections);
            for (const key in selections) {
                if (!(newSelections[key]?.length == 1 && newSelections[key][0] == 'Any')) {
                    continue;
                } else {
                    newSelections[key] = angular.copy(options[key]);
                }
            }
            return newSelections;
        }
        
        // Get text to show on filter dropdown depending on what has been selected
        function getMultiFilterSelectedText(filter, filterOptions, appliedFilters) {
            let selections = appliedFilters[filter.field];
            if (selections == null) {
                return '-';
            }
            else if (selections?.length == 1) {
                return getOptionName(filterOptions[filter.field]?.filter(x => x.value == selections[0])[0], filter.isBool, filter.isDecimal);
            } else {
                return `${selections?.length} Selected`;
            }
        }
        
        // Get text to show on filter dropdown depending on what has been selected
        function getMultiSelectDropdownText(selectedList, childKey) {
             if (selectedList == null || selectedList.length == 0) {
                 return "-";
             } else if (selectedList.find(i => i[childKey] == "All") != null) {
                 return "All";
             }
             else if (selectedList.length == 1) {
                 return selectedList[0][childKey];
             } else if (selectedList.length == 2) {
                 return `${selectedList[0][childKey]}, ${selectedList[1][childKey]}`;
             } else {
                 return `${selectedList.length} Selected`;
             }
        }

        // Get option name
        function getOptionName(option, isBool, isDecimal) {
            if (option.name == 'Any' || option.name == 'Not Specified') {
                return option.name;
            } else if (isBool) {
                return option.name == true
                            ? "Yes"
                            : option.name == false
                                ? "No"
                                : option.name;
            } else if (isDecimal) {
                return Number(option.name).toFixed(2);
            } else {
                return option.name;
            }
        }

        // Adjust selected filters (account for 'Any')
        function adjustFiltersForSelection(filters, appliedFilters, appliedFiltersOld) {
            for (let i = 0; i < filters.length; i++) {
                let field = filters[i].field;
                if (!filters[i].isDate) {
                    // IF nothing selected, or other values set then selected 'Any', set as empty
                    if (appliedFilters[field] == null || appliedFilters[field].length == 0 || (!appliedFiltersOld[field]?.includes('Any') && appliedFilters[field].includes('Any'))) {
                        appliedFilters[field] = ['Any'];
                    }
                    // ELSE IF 'Any' set then selected other value, just set to other value
                    else if (appliedFiltersOld[field]?.length > 0 && appliedFiltersOld[field][0] == 'Any' && appliedFilters[field].length >= 2) {
                        appliedFilters[field] = appliedFilters[field].filter(x => x != 'Any');

                    }
                }
            }
        }


        // Apply particular order on filter options
        function orderFilterOptions(filterOptions) {
            for (const key in filterOptions) {
                if (filterOptions[key].find(o => o.value == "No") || filterOptions[key].find(o => o.value == "Yes")) {
                    let offset = filterOptions[key].some(o => o.value == "Not Specified") ? 1 : 0;
                    filterOptions[key] = filterOptions[key].filter(o => o.value != "No");
                    filterOptions[key].splice(1+offset, 0, {name: "No", value: "No"});
                    filterOptions[key] = filterOptions[key].filter(o => o.value != "Yes");
                    filterOptions[key].splice(2+offset, 0, {name: "Yes", value: "Yes"});
                }
            }
        }


        // Check if any options selected on filter
        function anyOptionsSelectedOnField(field, appliedFilters) {
            return field.isDate
                    ? appliedFilters[field.field]?.label != "All Time"
                    : appliedFilters[field.field]?.some(selected => selected != "Any");
        }
        

        // Check if any filters selected
        function anyFiltersApplied(search, fields, appliedFilters) {
            return (search != null && search != '') || fields?.some(field => anyOptionsSelectedOnField(field, appliedFilters));
        }


        // Get new list based on filters
        function getNewFilteredList(filters, oldList, appliedFilters, searchString, searchFields) {
            let newList = angular.copy(oldList);
            // Filter on dropdowns
            for (let i = 0; i < filters.length; i++) {
                if (!filters[i].isDate) {
                    let field = filters[i].field;
                    let appliedFilter = appliedFilters[field];
                    if (appliedFilter.length > 0 && !(appliedFilter[0] == 'Any')) {
                        newList = newList?.filter(project => appliedFilter.includes(project[field]) || (appliedFilter.includes('Not Specified') && project[field] == null));
                    }
                }
            }
            // Filter on search
            if (searchString?.length > 0) {
                let search = searchString.toLowerCase();
                newList = newList?.filter(project => searchFields.some(field => project[field]?.toString().toLowerCase().indexOf(search) >= 0));
            }
            return newList;
        }
        
        // Simulate each other possible filter option
        function updateFilterCountData(filters, filterOptions, currentList, appliedFilters, searchString, searchFields, filterCountData) {
            let SIM = {
                appliedFiltersOld: angular.copy(appliedFilters),
                currentRealList: angular.copy(currentList)
            };
            filters.forEach(filter => {
                filterOptions[filter.field].forEach(option => {

                    // Reset sim filters back to real filters
                    SIM.appliedFilters = angular.copy(SIM.appliedFiltersOld);

                    // Add SIM filter option to applied filters
                    if (!SIM.appliedFilters[filter.field]?.includes(option.value)) {
                        SIM.appliedFilters[filter.field] == null ? SIM.appliedFilters[filter.field] = [option.value] : SIM.appliedFilters[filter.field].push(option.value);
                    }

                    // Adjust filters for new selection (account for 'Any')
                    adjustFiltersForSelection(filters, SIM.appliedFilters, SIM.appliedFiltersOld);

                    // Filter list using new SIM filters
                    SIM.newList = getNewFilteredList(filters, SIM.currentRealList, SIM.appliedFilters, searchString, searchFields);

                    // Get how many designs in new list have this filter option
                    let designsInSimListHaveThisOption = null;
                    if (option.value == 'Any') {
                        designsInSimListHaveThisOption = SIM.newList?.length ?? 0
                    }
                    else {
                        designsInSimListHaveThisOption = option.value == 'Not Specified'
                                                            ? SIM.newList?.filter(x => x[filter.field] == null).length ?? 0
                                                            : SIM.newList?.filter(x => x[filter.field] == option.value).length ?? 0;
                    }

                    // Set to data to show on UI
                    if (filterCountData[filter.field] == null) {
                        filterCountData[filter.field] = {};
                    }
                    filterCountData[filter.field][option.value] = designsInSimListHaveThisOption;

                });
            });
        }

        // Get a filtered list of options based on requirement that combination of options must be unique for every item in list.
        // otherItems: The full list of items to check against except the item we are checking.
        // thisItem: The item we are checking for what options are available for specific field by matching this item's other selections against the selections of all other items in main list.
        // optionsList: Full list of options for the field being checked before they are filtered based on what is available.
        // checkingFieldName: Name of the field being checked.
        // checkingFieldKeyName: If value is set from option's child variable
        // otherFieldNames: All other field names where the combination of the selections on the item being checked cannot match selections on any other item.
        function availableOptionsForItem(otherItems, thisItem, optionsList, checkingFieldName, checkingFieldKeyName, otherFieldNames) {
            // Filter list of options
            return optionsList?.filter(o =>
                // Only accept options where the combination of all selections including this option selected does not match selections on any other item
                !otherItems.some(item =>
                       item[checkingFieldName] == (checkingFieldKeyName ? o[checkingFieldKeyName] : o)
                    && otherFieldNames.every(optionName => item[optionName] == thisItem[optionName]
                )
            ));
        }
        
        function sortList(sort, list) {
            let field = null;
            let dir = null;
            if (sort?.sortBy != null) {
                field = sort.sortBy[0].field;
                dir = sort.sortBy[0].dir;
            }  else {
                field = 'projectName';
                dir = 'asc';
            }
            return list.sort((a, b) =>
                dir == 'asc' ? a[field] > b[field] || b[field] == null ? 1 : -1
                             : a[field] < b[field] || a[field] == null ? 1 : -1
            );
        }

        // ------------- //
        // ------------- //
        // ------------- //


        // Options for "Target Energy Rating" dropdown
        function targetEnergyRatingOptions() {
            let targetEnergyRatingOptions= [];
            for (let i = 0.0; i <= 10.0; i += 0.1) {
                targetEnergyRatingOptions.push( Number(i.toFixed(1)) ); // Just pushing 'i' causes some nums to be off by fraction, eg. 2.999999999
            }
            return targetEnergyRatingOptions;
        }


        function uploadSoftwareFile(assessment, $file, target, field, jobFiles, category, classification, fileChangedCallback, externalClickCallback) {
            if ($file == undefined || $file == null) {
                if (fileChangedCallback != null)
                    fileChangedCallback();
                return;
            }

            //find same documents with same id
            var sameDocument = [];
            if (target.documentId && assessment.assessmentDrawings) {
                for (var ii = 0; ii < assessment.assessmentDrawings.length; ii++) {
                    if (assessment.assessmentDrawings[ii].documentId == target.documentId) {
                        sameDocument.push(assessment.assessmentDrawings[ii]);
                    }
                }
            }
            
            if (sameDocument.length) {
                
                for (var ii = 0; ii < sameDocument.length; ii++) {
                    sameDocument[ii][field + "UploadProgress"] = null;
                }
                
            } else {
                target[field + "UploadProgress"] = null;
            }
            
            var url = `../api/Assessment/UploadFile?assessmentId=${assessment.assessmentId ?? assessment.newAssessmentId}&jobId=${assessment.jobId}`;
            
            if(category)
                url += `&category=${category}`

            if(classification)
                url += `&classification=${classification}`

            var promise = Upload.upload({
                url: url, // webapi url
                method: "POST",
                file: $file
            });
            promise.progress(function (evt) {
                if (sameDocument.length) {
                    for (var ii = 0; ii < sameDocument.length; ii++) {
                        sameDocument[ii][field + "UploadProgress"] = 100 * (evt.loaded / evt.total);
                    }
                } else {
                    target[field + "UploadProgress"] = 100 * (evt.loaded / evt.total);
                }
            }).success(function (data, status, headers, config) {
                
                if (jobFiles != undefined && jobFiles != null) {
                    
                    var contains = false;
                    
                    for (var ii = 0; ii < jobFiles.length; ii++) {
                        if (jobFiles[ii].fileId == data.fileId) {
                            contains = true;
                            break;
                        }
                    }
                    
                    if (contains === false) 
                        jobFiles.push(data);
                    
                }
                
                target[field] = data;
                if (sameDocument.length) {
                    
                    for (var ii = 0; ii < sameDocument.length; ii++) {
                        sameDocument[ii][field + "UploadProgress"] = null;
                    }
                    
                } else 
                    target[field + "UploadProgress"] = null;
                
                if (fileChangedCallback != null)
                    fileChangedCallback();

                if (externalClickCallback != null)
                    externalClickCallback();
                

            }).error(function (data, status, headers, config) {
                target[field + "UploadProgress"] = null;
                if (fileChangedCallback != null)
                    fileChangedCallback();
            });
        }


        // Add a 'preConvertCallback' if you want to convert each value first before checking if it is a duplicate
        function uniqueValues(list, preConvertCallback) {
            return preConvertCallback == null
                    ? list.filter((value, index, array) => array.indexOf(value) === index)
                    : list.filter((value, index, array) => array.map(i => preConvertCallback(i)).indexOf(preConvertCallback(value)) === index);
        }

        function initialiseSelectAll(allOptions, selectedOptions, childPrimaryKey, childDisplayKey) {
            let selectedPrimaryKeys = selectedOptions.map(o => o[childPrimaryKey]);
            let allSelected = allOptions.every(o => selectedPrimaryKeys.includes(o[childPrimaryKey]));
            let allObj = {};
            allObj[childPrimaryKey] = "All";
            allObj[childDisplayKey] = "All";
            if (allOptions.find(o => o[childPrimaryKey] == "All") == null) {
                allOptions.unshift(allObj);
            }
            if (allSelected && selectedOptions.find(o => o[childPrimaryKey] == "All") == null) {
                selectedOptions.unshift(allObj);
            }
        }

        // This function accounts for the list state before the option is added and that the option is about to be added, since this function would be called on an option's "ng-click"
        function selectAllLogic(allOptions, selectionsBefore, listChildIdKey, selectedItem, selectAllCode) {
            let getVal = (obj) => listChildIdKey == null || listChildIdKey == '' ? obj : obj[listChildIdKey];
            let optionBeingAdded = !selectionsBefore.some(g => getVal(g) == getVal(selectedItem));
            let tempNewSelections = !selectionsBefore.some(g => getVal(g) == getVal(selectedItem)) ? [...selectionsBefore, selectedItem] : selectionsBefore.filter(g => getVal(g) != getVal(selectedItem));
            // IF "Select All" selected, select all
            if (getVal(selectedItem) == selectAllCode && optionBeingAdded) {
                return allOptions.filter(g => getVal(g) != selectAllCode);
            }
            // ELSE IF "Select All" deselected, deselect all
            else if (getVal(selectedItem) == selectAllCode) {
                return allOptions.filter(g => getVal(g) == selectAllCode);
            }
            // ELSE IF any other option AND option is being added AND all options now selected, also select selectAllCode
            else if (optionBeingAdded && tempNewSelections.filter(g => getVal(g) != selectAllCode).length == allOptions.filter(g => getVal(g) != selectAllCode).length) {
                selectionsBefore.push(allOptions.find(g => getVal(g) == selectAllCode));
                return selectionsBefore;
            }
            // ELSE IF option being removed AND all were selected before, remove selectAllCode
            else if (!optionBeingAdded && selectionsBefore.filter(g => getVal(g) != selectAllCode).length == allOptions.filter(g => getVal(g) != selectAllCode).length) {
                return selectionsBefore.filter(g => getVal(g) != selectAllCode);
            // ELSE don't do anything
            } else {
                return selectionsBefore;
            }
        }


        // Get a particular symbol as a char
        function symbol(name) {
            switch (name) {
                case "lessThanEqual":    return String.fromCharCode(8804);
                case "greaterThanEqual": return String.fromCharCode(8805);
                case "squared":          return String.fromCharCode(178);
                case "cubed":            return String.fromCharCode(179);
                case "quarter":          return String.fromCharCode(188);
                case "half":             return String.fromCharCode(189);
                case "threeQuarters":    return String.fromCharCode(190);
                case "degrees":          return String.fromCharCode(176);
                case "micro":            return String.fromCharCode(181);
                case "alpha":            return String.fromCharCode(945);
                case "beta":             return String.fromCharCode(946);
                case "pi":               return String.fromCharCode(960);
            }
        }


        // Get a file's lines as an array
        async function getFileLines(file, removeEmpties = false) {
            return new Promise(resolve => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    let lines = event.target.result.split(/\r\n|\n/);
                    if (removeEmpties) {
                        lines = lines.filter(l => l?.length > 0);
                    }
                    resolve(lines);
                };
                reader.readAsText(file);
            });
        }

        function stringNullOrEmpty(inString) {
            return (inString == null || inString == "") && inString != 0;
        }

        function encodeForUrl(text) {
            let encoded = encodeURIComponent(text);
            return encoded;
        }

        // We want variables in an array to be of reference to a variable in another array by matching with a child key
        // Eg. When loading dropdown selections from backend, need to re-link selections to their corresponding objects in the full list of options
        function linkVariablesBetweenArrays(theArray, linkToArray, byKey) {
            let newArray = [];
            theArray.forEach(i => {
                let item = linkToArray.find(l => l[byKey] == i[byKey]);
                if (item != null) {
                    newArray.push(item);
                }
            });
            newArray = uniqueValues(newArray, (item) => item[byKey]); // In case there were any duplicates for some reason
            return newArray;
        }
    }
})();